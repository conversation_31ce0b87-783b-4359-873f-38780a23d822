"""
故障异常报警系统模块
"""

import sys
import random
from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QFrame, QGridLayout, QProgressBar,
                             QScrollArea, QSizePolicy, QDialog, QTableWidget,
                             QTableWidgetItem, QHeaderView, QLineEdit, QComboBox,
                             QDateEdit, QMessageBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QDate
from PyQt5.QtGui import QFont, QIcon, QPalette
from ui.styles import (PRIMARY_BG, SECONDARY_BG, ACCENT_COLOR, HIGHLIGHT_COLOR,
                       TEXT_PRIMARY, TEXT_SECONDARY, SUCCESS_COLOR, WARNING_COLOR,
                       ERROR_COLOR, INFO_COLOR)


class StatusCard(QFrame):
    """状态卡片组件"""

    def __init__(self, title, icon, status="normal"):
        super().__init__()
        self.title = title
        self.icon = icon
        self.status = status
        self.init_ui()

    def init_ui(self):
        """初始化卡片界面"""
        self.setFixedSize(380, 420)  # 增加卡片尺寸，提供更多空间
        # 卡片式设计：添加阴影效果和圆角
        self.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid #e0e6ed;
                border-radius: 20px;
                margin: 15px;
                /* 模拟阴影效果 */
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 white, stop: 1 #f8f9fa);
            }}
            QFrame:hover {{
                border-color: {ACCENT_COLOR};
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f0f8ff);
                transform: translateY(-2px);
            }}
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 25, 25, 25)  # 增加内边距，提升视觉层次
        layout.setSpacing(15)  # 增加内部间距，避免紧凑

        # 卡片标题和状态
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 10)

        # 使用更大的图标和改进的标题样式
        title_label = QLabel(f"{self.icon} {self.title}")
        title_label.setStyleSheet(f"""
            font-size: 20px;
            font-weight: bold;
            color: {TEXT_PRIMARY};
            padding: 8px 0px;
        """)
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # 状态标签
        self.status_label = QLabel()
        self.update_status(self.status)
        header_layout.addWidget(self.status_label)

        layout.addLayout(header_layout)

        # 改进的分隔线设计
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFixedHeight(2)
        line.setStyleSheet(f"""
            QFrame {{
                background-color: #e9ecef;
                border: none;
                margin: 5px 0px;
            }}
        """)
        layout.addWidget(line)

        # 数据区域
        self.data_layout = QVBoxLayout()
        self.data_layout.setSpacing(12)  # 增加数据项之间的间距，避免过于紧凑
        self.data_layout.setContentsMargins(8, 15, 8, 15)  # 数据区域的内边距
        layout.addLayout(self.data_layout)

        layout.addStretch()
    
    def update_status(self, status):
        """更新状态 - 统一颜色饱和度和亮度"""
        self.status = status
        # 统一的状态颜色：降低饱和度，避免过亮刺眼
        normal_color = "#28a745"    # 柔和的绿色
        warning_color = "#ffc107"   # 柔和的黄色
        alarm_color = "#dc3545"     # 柔和的红色

        if status == "normal":
            self.status_label.setText("运行正常")
            self.status_label.setStyleSheet(f"""
                background-color: {normal_color};
                color: white;
                padding: 10px 18px;
                border-radius: 18px;
                font-weight: bold;
                font-size: 14px;
                border: 2px solid {normal_color};
            """)
        elif status == "warning":
            self.status_label.setText("警告")
            self.status_label.setStyleSheet(f"""
                background-color: {warning_color};
                color: {TEXT_PRIMARY};
                padding: 10px 18px;
                border-radius: 18px;
                font-weight: bold;
                font-size: 14px;
                border: 2px solid {warning_color};
            """)
        elif status == "alarm":
            self.status_label.setText("故障报警")
            self.status_label.setStyleSheet(f"""
                background-color: {alarm_color};
                color: white;
                padding: 10px 18px;
                border-radius: 18px;
                font-weight: bold;
                font-size: 14px;
                border: 2px solid {alarm_color};
            """)
    
    def add_data_item(self, label, value, unit="", icon=""):
        """添加数据项 - 改进字体层级和减少蓝色高亮"""
        item_layout = QHBoxLayout()
        item_layout.setContentsMargins(8, 10, 8, 10)  # 增加上下边距，避免紧凑

        # 添加图标支持，让信息更直观
        if icon:
            icon_label = QLabel(icon)
            icon_label.setStyleSheet("font-size: 16px; margin-right: 5px;")
            item_layout.addWidget(icon_label)

        label_widget = QLabel(label)
        # 减少蓝色高亮，使用浅色块或仅用边框标识
        label_widget.setStyleSheet(f"""
            color: {TEXT_SECONDARY};
            font-size: 14px;
            font-weight: 600;
            min-height: 28px;
            padding: 6px 10px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        """)
        label_widget.setMinimumWidth(130)  # 设置最小宽度确保标签完整显示

        value_widget = QLabel(f"{value} {unit}")
        # 数据值加大显示，提升可读性
        value_widget.setStyleSheet(f"""
            color: {TEXT_PRIMARY};
            font-size: 16px;
            font-weight: bold;
            min-height: 28px;
            padding: 6px 10px;
            text-align: right;
        """)
        value_widget.setAlignment(Qt.AlignRight)
        value_widget.setMinimumWidth(110)  # 设置最小宽度确保数值完整显示

        item_layout.addWidget(label_widget)
        item_layout.addStretch()
        item_layout.addWidget(value_widget)

        self.data_layout.addLayout(item_layout)
    
    def add_progress_bar(self, label, value, max_value=100):
        """添加进度条 - 美化设计，使用渐变绿-黄-红，加上圆角"""
        progress_layout = QVBoxLayout()
        progress_layout.setContentsMargins(8, 10, 8, 10)  # 增加边距
        progress_layout.setSpacing(10)  # 增加间距

        # 标签和数值
        label_layout = QHBoxLayout()
        label_layout.setContentsMargins(0, 0, 0, 8)  # 标签下方留空间

        label_widget = QLabel(label)
        # 减少蓝色高亮，统一样式
        label_widget.setStyleSheet(f"""
            color: {TEXT_SECONDARY};
            font-size: 14px;
            font-weight: 600;
            min-height: 28px;
            padding: 6px 10px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        """)
        label_widget.setMinimumWidth(130)  # 确保标签有足够宽度

        value_widget = QLabel(f"{value}%")
        value_widget.setStyleSheet(f"""
            color: {TEXT_PRIMARY};
            font-size: 16px;
            font-weight: bold;
            min-height: 28px;
            padding: 6px 10px;
            text-align: right;
        """)
        value_widget.setAlignment(Qt.AlignRight)
        value_widget.setMinimumWidth(70)  # 确保百分比有足够宽度

        label_layout.addWidget(label_widget)
        label_layout.addStretch()
        label_layout.addWidget(value_widget)

        progress_layout.addLayout(label_layout)

        # 进度条 - 美化设计
        progress_bar = QProgressBar()
        progress_bar.setRange(0, max_value)
        progress_bar.setValue(value)
        progress_bar.setFixedHeight(20)  # 增加进度条高度
        progress_bar.setTextVisible(False)  # 隐藏进度条上的文字，避免重叠

        # 渐变颜色设计：绿-黄-红
        if value < 30:
            # 绿色渐变
            gradient_color = "qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0, stop: 0 #28a745, stop: 1 #20c997)"
        elif value < 70:
            # 黄色渐变
            gradient_color = "qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0, stop: 0 #ffc107, stop: 1 #fd7e14)"
        else:
            # 红色渐变
            gradient_color = "qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0, stop: 0 #dc3545, stop: 1 #e74c3c)"

        progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid #e9ecef;
                border-radius: 10px;
                background-color: #f8f9fa;
                margin: 3px 0px;
                text-align: center;
            }}
            QProgressBar::chunk {{
                background: {gradient_color};
                border-radius: 8px;
                margin: 1px;
            }}
        """)

        progress_layout.addWidget(progress_bar)
        self.data_layout.addLayout(progress_layout)


class FaultHistoryWindow(QDialog):
    """历史故障记录窗口"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.load_sample_data()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("历史故障记录")
        self.setGeometry(100, 100, 1000, 600)
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {PRIMARY_BG};
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 标题
        title_label = QLabel("📋 历史故障记录")
        title_label.setStyleSheet(f"""
            font-size: 24px;
            font-weight: bold;
            color: {TEXT_PRIMARY};
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)
        
        # 筛选区域
        filter_layout = QHBoxLayout()
        
        # 日期筛选
        date_label = QLabel("日期范围:")
        date_label.setStyleSheet(f"color: {TEXT_PRIMARY}; font-size: 16px;")
        filter_layout.addWidget(date_label)
        
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setStyleSheet(f"""
            QDateEdit {{
                padding: 8px;
                border: 2px solid {ACCENT_COLOR};
                border-radius: 5px;
                font-size: 14px;
                background-color: white;
            }}
        """)
        filter_layout.addWidget(self.start_date)
        
        filter_layout.addWidget(QLabel("至"))
        
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setStyleSheet(self.start_date.styleSheet())
        filter_layout.addWidget(self.end_date)
        
        # 故障类型筛选
        type_label = QLabel("故障类型:")
        type_label.setStyleSheet(f"color: {TEXT_PRIMARY}; font-size: 16px;")
        filter_layout.addWidget(type_label)
        
        self.type_combo = QComboBox()
        self.type_combo.addItems(["全部", "外圈损伤", "内圈磨损", "保持架断裂", "内圈点蚀"])
        self.type_combo.setStyleSheet(f"""
            QComboBox {{
                padding: 8px;
                border: 2px solid {ACCENT_COLOR};
                border-radius: 5px;
                font-size: 14px;
                background-color: white;
                min-width: 120px;
            }}
        """)
        filter_layout.addWidget(self.type_combo)
        
        filter_layout.addStretch()
        
        # 搜索按钮
        search_btn = QPushButton("🔍 搜索")
        search_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ACCENT_COLOR};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {HIGHLIGHT_COLOR};
            }}
        """)
        search_btn.clicked.connect(self.search_records)
        filter_layout.addWidget(search_btn)
        
        layout.addLayout(filter_layout)
        
        # 表格
        self.table = QTableWidget()
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels(["时间", "检测方法", "故障类型", "置信度", "状态", "详情"])
        
        # 设置表格样式
        self.table.setStyleSheet(f"""
            QTableWidget {{
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                font-size: 14px;
                gridline-color: #f0f0f0;
            }}
            QTableWidget::item {{
                padding: 12px;
                border-bottom: 1px solid #f0f0f0;
            }}
            QTableWidget::item:selected {{
                background-color: {SECONDARY_BG};
            }}
            QHeaderView::section {{
                background-color: {ACCENT_COLOR};
                color: white;
                padding: 12px;
                border: none;
                font-weight: bold;
            }}
        """)
        
        # 设置表格属性
        header = self.table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        
        layout.addWidget(self.table)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        export_btn = QPushButton("📄 导出记录")
        export_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {INFO_COLOR};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 16px;
            }}
            QPushButton:hover {{
                background-color: #5dade2;
            }}
        """)
        button_layout.addWidget(export_btn)
        
        close_btn = QPushButton("❌ 关闭")
        close_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {TEXT_SECONDARY};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 16px;
            }}
            QPushButton:hover {{
                background-color: #2c3e50;
            }}
        """)
        close_btn.clicked.connect(self.close)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
    
    def load_sample_data(self):
        """加载示例数据"""
        sample_data = [
            ["2024-08-01 14:28:45", "深度学习监测", "外圈损伤", "94.7%", "报警", "高置信度检测"],
            ["2024-08-01 14:28:45", "故障判断", "外圈损伤", "86.3%", "报警", "多算法融合"],
            ["2024-08-01 11:45:22", "传统分类器", "内圈磨损", "68.5%", "警告", "SVM分类器"],
            ["2024-07-31 16:32:11", "深度学习监测", "保持架断裂", "82.1%", "报警", "ResNet-1D模型"],
            ["2024-07-30 09:15:37", "故障判断", "内圈点蚀", "73.8%", "警告", "特征分析"],
            ["2024-07-29 15:22:18", "深度学习监测", "外圈损伤", "91.2%", "报警", "CNN模型"],
            ["2024-07-28 10:45:33", "传统分类器", "内圈磨损", "65.4%", "警告", "随机森林"],
            ["2024-07-27 13:18:45", "故障判断", "保持架断裂", "78.9%", "报警", "频域分析"],
        ]
        
        self.table.setRowCount(len(sample_data))
        
        for row, data in enumerate(sample_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(str(value))
                
                # 设置状态列的颜色
                if col == 4:  # 状态列
                    if value == "报警":
                        item.setBackground(QPalette().color(QPalette.Base))
                        item.setForeground(QPalette().color(QPalette.Text))
                    elif value == "警告":
                        item.setBackground(QPalette().color(QPalette.Base))
                        item.setForeground(QPalette().color(QPalette.Text))
                
                self.table.setItem(row, col, item)
    
    def search_records(self):
        """搜索记录"""
        QMessageBox.information(self, "搜索", "搜索功能已触发！\n实际应用中会根据筛选条件查询数据库。")


class FaultAlarmSystem(QWidget):
    """故障异常报警系统主页面"""
    
    def __init__(self, db_manager=None):
        super().__init__()
        self.db_manager = db_manager
        self.current_status = "alarm"  # normal, warning, alarm
        self.init_ui()
        self.setup_timer()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 25, 25, 25)  # 增加边距
        layout.setSpacing(25)  # 增加间距，避免紧凑

        # 标题 - 改进字体层级
        title_label = QLabel("⚠️ 故障异常报警系统")
        title_label.setStyleSheet(f"""
            font-size: 24px;
            font-weight: bold;
            color: {TEXT_PRIMARY};
            margin-bottom: 15px;
            padding: 10px 0px;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 状态栏 - 单独置于页面上方，减少与下方数据的干扰
        self.create_status_bar(layout)

        # 添加分隔空间
        layout.addSpacing(20)

        # 监测卡片区域 - 增加分隔线或使用卡片式设计
        self.create_monitoring_cards(layout)

        # 添加间距，将按钮下移
        layout.addSpacing(30)

        # 控制按钮区域 - 统一按钮风格和间距
        self.create_control_buttons(layout)

        layout.addStretch()

    def create_status_bar(self, parent_layout):
        """创建状态栏 - 单独置于页面上方的状态栏"""
        self.status_frame = QFrame()
        self.status_frame.setFixedHeight(100)  # 减小高度，更紧凑
        self.update_status_bar()

        status_layout = QHBoxLayout(self.status_frame)
        status_layout.setContentsMargins(25, 15, 25, 15)
        status_layout.setSpacing(20)

        # 状态指示器
        indicator_layout = QHBoxLayout()
        indicator_layout.setSpacing(15)

        self.status_icon = QLabel("⚠️")
        self.status_icon.setStyleSheet("font-size: 36px;")  # 稍微减小图标
        indicator_layout.addWidget(self.status_icon)

        status_text_layout = QVBoxLayout()
        status_text_layout.setSpacing(5)

        self.status_text = QLabel("轴承故障报警")
        self.status_text.setStyleSheet(f"""
            font-size: 20px;
            font-weight: bold;
            color: {TEXT_PRIMARY};
        """)
        status_text_layout.addWidget(self.status_text)

        self.status_desc = QLabel("系统检测到异常情况，请立即检查！")
        self.status_desc.setStyleSheet(f"""
            font-size: 13px;
            color: {TEXT_SECONDARY};
        """)
        status_text_layout.addWidget(self.status_desc)

        indicator_layout.addLayout(status_text_layout)
        status_layout.addLayout(indicator_layout)

        status_layout.addStretch()

        # 设备信息和更新时间 - 右侧信息区
        info_layout = QVBoxLayout()
        info_layout.setAlignment(Qt.AlignRight)
        info_layout.setSpacing(5)

        device_label = QLabel("设备ID: BX-2037-08")
        device_label.setStyleSheet(f"""
            color: {TEXT_SECONDARY};
            font-size: 13px;
            font-weight: 600;
        """)
        info_layout.addWidget(device_label)

        self.last_update = QLabel()
        self.last_update.setStyleSheet(f"""
            color: {TEXT_SECONDARY};
            font-size: 12px;
        """)
        info_layout.addWidget(self.last_update)

        status_layout.addLayout(info_layout)

        parent_layout.addWidget(self.status_frame)

        # 更新时间
        self.update_time()

    def update_status_bar(self):
        """更新状态栏样式"""
        if self.current_status == "normal":
            border_color = SUCCESS_COLOR
            bg_color = "#f8fff8"
        elif self.current_status == "warning":
            border_color = WARNING_COLOR
            bg_color = "#fffdf8"
        else:  # alarm
            border_color = ERROR_COLOR
            bg_color = "#fff8f8"

        self.status_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {bg_color};
                border: 3px solid {border_color};
                border-radius: 15px;
            }}
        """)

    def create_monitoring_cards(self, parent_layout):
        """创建监测卡片区域"""
        cards_layout = QHBoxLayout()
        cards_layout.setSpacing(15)  # 减小间距以适应更窄的卡片

        # 故障判断卡片
        self.fault_card = StatusCard("故障判断", "🔍", "alarm")
        self.fault_card.add_data_item("振动幅度", "8.7", "mm/s")
        self.fault_card.add_data_item("温度变化", "+12.5", "°C")
        self.fault_card.add_data_item("噪声水平", "78", "dB")
        self.fault_card.add_progress_bar("故障概率", 92)
        self.fault_card.add_data_item("故障类型", "外圈损伤")
        self.fault_card.add_data_item("置信度", "86.3%")
        cards_layout.addWidget(self.fault_card)

        # 传统分类器监测卡片
        self.classical_card = StatusCard("传统分类器监测", "⚙️", "normal")
        self.classical_card.add_data_item("特征向量", "[0.87, 0.45, 0.32]")
        self.classical_card.add_data_item("分类结果", "正常")
        self.classical_card.add_data_item("置信度", "76.2%")
        self.classical_card.add_progress_bar("异常概率", 24)
        self.classical_card.add_data_item("使用模型", "SVM分类器")
        self.classical_card.add_data_item("更新时间", "14:28:42")
        cards_layout.addWidget(self.classical_card)

        # 深度学习监测卡片
        self.dl_card = StatusCard("深度学习监测", "🧠", "alarm")
        self.dl_card.add_data_item("模型输出", "故障 (外圈)")
        self.dl_card.add_data_item("置信度", "94.7%")
        self.dl_card.add_data_item("特征相似度", "0.89")
        self.dl_card.add_progress_bar("故障概率", 95)
        self.dl_card.add_data_item("使用模型", "ResNet-1D")
        self.dl_card.add_data_item("推理时间", "128 ms")
        cards_layout.addWidget(self.dl_card)

        parent_layout.addLayout(cards_layout)

    def create_control_buttons(self, parent_layout):
        """创建控制按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(20)
        button_layout.setAlignment(Qt.AlignCenter)

        # 刷新数据按钮
        refresh_btn = QPushButton("🔄 刷新数据")
        refresh_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {INFO_COLOR};
                color: white;
                border: none;
                border-radius: 25px;
                padding: 15px 30px;
                font-weight: bold;
                font-size: 16px;
                min-width: 150px;
            }}
            QPushButton:hover {{
                background-color: #5dade2;
            }}
        """)
        refresh_btn.clicked.connect(self.refresh_data)
        button_layout.addWidget(refresh_btn)

        # 历史故障记录按钮
        history_btn = QPushButton("📋 历史故障记录")
        history_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ACCENT_COLOR};
                color: white;
                border: none;
                border-radius: 25px;
                padding: 15px 30px;
                font-weight: bold;
                font-size: 16px;
                min-width: 150px;
            }}
            QPushButton:hover {{
                background-color: {HIGHLIGHT_COLOR};
            }}
        """)
        history_btn.clicked.connect(self.show_history)
        button_layout.addWidget(history_btn)

        # 消音报警按钮
        mute_btn = QPushButton("🔇 消音报警")
        mute_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ERROR_COLOR};
                color: white;
                border: none;
                border-radius: 25px;
                padding: 15px 30px;
                font-weight: bold;
                font-size: 16px;
                min-width: 150px;
            }}
            QPushButton:hover {{
                background-color: #e55353;
            }}
        """)
        mute_btn.clicked.connect(self.mute_alarm)
        button_layout.addWidget(mute_btn)

        # 生成报告按钮
        report_btn = QPushButton("📄 生成报告")
        report_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {SUCCESS_COLOR};
                color: white;
                border: none;
                border-radius: 25px;
                padding: 15px 30px;
                font-weight: bold;
                font-size: 16px;
                min-width: 150px;
            }}
            QPushButton:hover {{
                background-color: #00a085;
            }}
        """)
        report_btn.clicked.connect(self.generate_report)
        button_layout.addWidget(report_btn)

        parent_layout.addLayout(button_layout)

    def setup_timer(self):
        """设置定时器"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)  # 每秒更新一次

        # 模拟数据更新定时器
        self.data_timer = QTimer()
        self.data_timer.timeout.connect(self.simulate_data_update)
        self.data_timer.start(5000)  # 每5秒更新一次数据

    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.last_update.setText(f"最后更新: {current_time}")

    def simulate_data_update(self):
        """模拟数据更新"""
        # 随机改变系统状态
        statuses = ["normal", "warning", "alarm"]
        weights = [0.3, 0.3, 0.4]  # 报警状态概率更高
        self.current_status = random.choices(statuses, weights=weights)[0]

        # 更新状态栏
        if self.current_status == "normal":
            self.status_icon.setText("✅")
            self.status_text.setText("系统运行正常")
            self.status_desc.setText("所有监测指标正常，设备运行良好")
        elif self.current_status == "warning":
            self.status_icon.setText("⚠️")
            self.status_text.setText("系统警告")
            self.status_desc.setText("检测到轻微异常，建议关注")
        else:  # alarm
            self.status_icon.setText("🚨")
            self.status_text.setText("轴承故障报警")
            self.status_desc.setText("系统检测到异常情况，请立即检查！")

        self.update_status_bar()

        # 更新卡片状态
        if hasattr(self, 'fault_card'):
            self.fault_card.update_status(self.current_status)
        if hasattr(self, 'classical_card'):
            self.classical_card.update_status("normal" if random.random() > 0.3 else "warning")
        if hasattr(self, 'dl_card'):
            self.dl_card.update_status(self.current_status)

    def refresh_data(self):
        """刷新数据"""
        QMessageBox.information(self, "刷新数据", "数据已刷新！\n实际应用中会从传感器和数据库获取最新数据。")
        self.simulate_data_update()

    def show_history(self):
        """显示历史故障记录"""
        history_window = FaultHistoryWindow(self)
        history_window.exec_()

    def mute_alarm(self):
        """消音报警"""
        QMessageBox.information(self, "消音报警", "报警已消音！\n实际应用中会停止声音报警，但视觉报警仍然保持。")

    def generate_report(self):
        """生成报告"""
        QMessageBox.information(self, "生成报告", "报告生成功能已触发！\n实际应用中会生成详细的故障诊断报告。")
